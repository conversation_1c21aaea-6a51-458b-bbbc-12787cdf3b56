'server-only';

import { NextAuthOptions, getServerSession } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse

const secret = process.env.AUTH_SECRET;
const URL = process.env.API_URL;
/**
 * Takes a token, and returns a new token with updated
 * `accessToken` and `accessTokenExpires`. If an error occurs,
 * returns the old token and an error property
 */
// async function refreshAccessToken(token: JWT): Promise<JWT> {
//   try {
//     const endpoint = `${URL}/auth/token/refresh`;
//     const response = await fetch(endpoint, {
//       headers: {
//         'Content-Type': 'application/json',
//         Authorization: `Bearer ${token?.refreshToken || ''}`,
//       },
//     });

//     if (!response.ok) return Promise.resolve({ ...token, isLogin: false });
//     const data = await response.json();
//     if (!response.ok) {
//       throw data;
//     }

//     return {
//       ...token,
//       accessToken: data?.accessToken,
//       refreshToken: data?.refreshToken,
//     };
//   } catch (error) {
//     return {
//       ...token,
//       isLogin: false,
//       error: 'RefreshAccessTokenError',
//     };
//   }
// }

export const authOptions: NextAuthOptions = {
  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'email',
        },
        password: { label: 'Password', type: 'password' },
      },
      // @ts-ignore
      async authorize(credentials) {
        try {
          const endpoint = `${URL}/auth/sign-in`;

          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials?.email,
              password: credentials?.password,
            }),
          });
          const result = await response.json();


          // Nếu đăng nhập thành công, trả về thông tin người dùng
          if (response.ok) {
            // Extract user data directly from the sign-in response
            const userData = result?.data?.user || {};

            // Check if school data exists
            const hasSchoolData = userData.school && Object.keys(userData.school).length > 0;
            const schoolData = hasSchoolData ? userData.school : null;

            // Only extract brand data if school data exists
            const brandData = schoolData?.brand || {};

            const user = {
              id: userData.id || '',
              name: userData.name || '',
              email: userData.email || '',
              role: userData.role || '',
              schoolId: userData.schoolId || null,
              // Add token information
              accessToken: result?.data?.accessToken,
              isLogin: true,
              // Add school information only if school data exists
              school: hasSchoolData ? {
                name: schoolData.name || '',
                address: schoolData.address || '',
                phoneNumber: schoolData.phoneNumber || '',
                registeredNumber: schoolData.registeredNumber || '',
                email: schoolData.email || '',
                brand: {
                  id: brandData.id || '',
                  logo: brandData.logo,
                  color: brandData.color || '',
                  image: brandData.image
                }
              } : undefined,
            };
            return Promise.resolve(user);
          }

          // Nếu đăng nhập không thành công, trả về null
          return Promise.resolve({ isLogin: false });
        } catch (error) {
          return Promise.resolve({ isLogin: false });
        }
      },
    }),
  ],

  callbacks: {
    async signIn({ user, ...account }) {
      // if(user.isLogin)
      return user?.isLogin || false;
    },
    async session({ session, token }) {
      // Create a properly structured session with user information from the token
      return {
        ...session,
        user: {
          id: token.id as string,
          name: token.name as string,
          email: token.email as string,
          accessToken: token.accessToken as string,
          role: token.role as string,
          schoolId: token.schoolId as string | null,
          isLogin: token.isLogin as boolean,
          school: token.school,
        },
      };
    },
    async jwt({ token, user, account, trigger, session: newSessionData }) {
      // Initial sign-in: Copy all relevant user details to the token
      if (account && user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.role = user.role;
        token.schoolId = user.schoolId;
        token.isLogin = user.isLogin;
        token.accessToken = user.accessToken;
        // Ensure user.school is correctly typed or handled if undefined
        token.school = user.school as ISchoolResponse | undefined;
      }

      // Handle session updates
      if (trigger === "update" && newSessionData?.user) {
        // Update user profile data if provided
        if (newSessionData.user.name !== undefined) {
          token.name = newSessionData.user.name;
        }

        if (newSessionData.user.email !== undefined) {
          token.email = newSessionData.user.email;
        }

        // Update schoolId if provided
        if (newSessionData.user.schoolId !== undefined) {
          token.schoolId = newSessionData.user.schoolId;
        }

        // Update school data if provided
        if (newSessionData.user.school) {
          if (token.school && typeof token.school === 'object') {
            token.school = {
              ...(token.school as ISchoolResponse),
              ...newSessionData.user.school,
            };
          } else {
            token.school = newSessionData.user.school as ISchoolResponse;
          }

          // Also update schoolId from school.id if not explicitly provided
          if (newSessionData.user.schoolId === undefined && newSessionData.user.school.id) {
            token.schoolId = newSessionData.user.school.id;
          }
        }

        // Handle legacy school name update (for backward compatibility)
        if (newSessionData.user.schoolName && token.schoolId && token.id) {
          if (token.school && typeof token.school === 'object') {
            token.school = {
              ...(token.school as ISchoolResponse), // Cast to ISchoolResponse
              name: newSessionData.user.schoolName,
            };
          } else if (!token.school) {
            token.school = {
              id: token.schoolId, // Assuming schoolId is the ID of the school
              name: newSessionData.user.schoolName,
              // Other fields would be unknown here without a re-fetch
            } as Partial<ISchoolResponse> as ISchoolResponse; // Cast carefully
          }
        }
      }
      return token; // Always return the token
    },
  },
  pages: {
    signIn: `/auth/sign-in`,
    error: `/auth/sign-in?type=error`,
  },
  secret,
  debug: true,
};

export const onSeverSession = () => {
  const session = getServerSession(authOptions);
  return session;
};
